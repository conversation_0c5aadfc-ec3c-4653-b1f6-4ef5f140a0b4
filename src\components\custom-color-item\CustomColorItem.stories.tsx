import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import CustomColorItem from "./CustomColorItem";

const meta: Meta<typeof CustomColorItem> = {
  title: "Design System/Custom Color Item",
  component: CustomColorItem,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
  argTypes: {
    showTailwindClasses: {
      control: { type: "boolean" },
    },
    showUsageExamples: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof CustomColorItem>;

export const WhiteColors: Story = {
  args: {
    title: "White",
    subtitle: "Pure white and white variants",
    colors: [
      {
        name: "white",
        cssValue: "var(--color-white)",
        bgClass: "bg-white",
        textClass: "text-white",
        borderClass: "border-white",
      },
      {
        name: "white/38",
        cssValue: "var(--color-white/38)",
        bgClass: "bg-white/38",
        textClass: "text-white/38",
        borderClass: "border-white/38",
      },
      {
        name: "white/25",
        cssValue: "var(--color-white/25)",
        bgClass: "bg-white/25",
        textClass: "text-white/25",
        borderClass: "border-white/25",
      },
    ],
    usageExamples: [
      `<div className="bg-white text-gray-800 border border-gray-200">
  Pure white background
</div>`,
      `<div className="bg-white/38 text-gray-900 border border-gray-300">
  Semi-transparent white overlay
</div>`,
    ],
  },
};

export const BlackColors: Story = {
  args: {
    title: "Black",
    subtitle: "Pure black and black variants",
    colors: [
      {
        name: "black",
        cssValue: "var(--color-black)",
        bgClass: "bg-black",
        textClass: "text-black",
        borderClass: "border-black",
      },
      {
        name: "black/87",
        cssValue: "var(--color-black/87)",
        bgClass: "bg-black/87",
        textClass: "text-black/87",
        borderClass: "border-black/87",
      },
      {
        name: "black/12",
        cssValue: "var(--color-black/12)",
        bgClass: "bg-black/12",
        textClass: "text-black/12",
        borderClass: "border-black/12",
      },
      {
        name: "black/6",
        cssValue: "var(--color-black/6)",
        bgClass: "bg-black/6",
        textClass: "text-black/6",
        borderClass: "border-black/6",
      },
    ],
    usageExamples: [
      `<div className="bg-black text-white border border-gray-800">
  Pure black background
</div>`,
      `<div className="bg-black/12 text-gray-900 border border-gray-300">
  Light black overlay for hover states
</div>`,
      `<div className="bg-black/87 text-white border border-gray-600">
  High opacity black for disabled states
</div>`,
    ],
  },
};

export const GrayColors: Story = {
  args: {
    title: "Gray",
    subtitle: "Gray color variants",
    colors: [
      {
        name: "gray-50",
        cssValue: "var(--color-gray-50)",
        bgClass: "bg-gray-50",
        textClass: "text-gray-50",
        borderClass: "border-gray-50",
      },
      {
        name: "gray-100",
        cssValue: "var(--color-gray-100)",
        bgClass: "bg-gray-100",
        textClass: "text-gray-100",
        borderClass: "border-gray-100",
      },
      {
        name: "gray-200",
        cssValue: "var(--color-gray-200)",
        bgClass: "bg-gray-200",
        textClass: "text-gray-200",
        borderClass: "border-gray-200",
      },
      {
        name: "gray-400",
        cssValue: "var(--color-gray-400)",
        bgClass: "bg-gray-400",
        textClass: "text-gray-400",
        borderClass: "border-gray-400",
      },
      {
        name: "gray-600",
        cssValue: "var(--color-gray-600)",
        bgClass: "bg-gray-600",
        textClass: "text-gray-600",
        borderClass: "border-gray-600",
      },
      {
        name: "gray-800",
        cssValue: "var(--color-gray-800)",
        bgClass: "bg-gray-800",
        textClass: "text-gray-800",
        borderClass: "border-gray-800",
      },
    ],
  },
};

export const BrandColors: Story = {
  args: {
    title: "Brand Colors",
    subtitle: "Primary brand colors",
    colors: [
      {
        name: "brand-red",
        cssValue: "var(--color-brand-red)",
        bgClass: "bg-brand-red",
        textClass: "text-brand-red",
        borderClass: "border-brand-red",
      },
      {
        name: "brand-yellow",
        cssValue: "var(--color-brand-yellow)",
        bgClass: "bg-brand-yellow",
        textClass: "text-brand-yellow",
        borderClass: "border-brand-yellow",
      },
      {
        name: "brand-sea",
        cssValue: "var(--color-brand-sea)",
        bgClass: "bg-brand-sea",
        textClass: "text-brand-sea",
        borderClass: "border-brand-sea",
      },
      {
        name: "brand-cream",
        cssValue: "var(--color-brand-cream)",
        bgClass: "bg-brand-cream",
        textClass: "text-brand-cream",
        borderClass: "border-brand-cream",
      },
    ],
  },
};

export const WithoutTailwindClasses: Story = {
  args: {
    title: "CSS Only Colors",
    subtitle: "Colors without Tailwind class examples",
    showTailwindClasses: false,
    showUsageExamples: false,
    colors: [
      {
        name: "custom-red",
        cssValue: "var(--color-brand-red)",
      },
      {
        name: "custom-blue",
        colorValue: "#3b82f6",
      },
    ],
  },
};

export const CustomUsageExamples: Story = {
  args: {
    title: "Interactive Colors",
    subtitle: "Colors with custom usage examples",
    colors: [
      {
        name: "focus",
        cssValue: "var(--color-focus)",
        bgClass: "bg-focus",
        textClass: "text-focus",
        borderClass: "border-focus",
      },
      {
        name: "link",
        cssValue: "var(--color-link)",
        bgClass: "bg-link",
        textClass: "text-link",
        borderClass: "border-link",
      },
    ],
    usageExamples: [
      `<button className="focus:ring-2 focus:ring-focus focus:ring-offset-2">
  Focused button
</button>`,
      `<a href="#" className="text-link hover:text-link/80 underline">
  Link text
</a>`,
    ],
  },
};
