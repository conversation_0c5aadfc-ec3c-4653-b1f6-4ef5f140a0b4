import type { HTMLAttributes } from "react";

export interface ColorSwatch {
  /** The name/label for this color */
  name: string;
  /** CSS custom property value (e.g., 'var(--color-white)') */
  cssValue?: string;
  /** Tailwind class name for background (e.g., 'bg-white') */
  bgClass?: string;
  /** Tailwind class name for text (e.g., 'text-white') */
  textClass?: string;
  /** Tailwind class name for border (e.g., 'border-white') */
  borderClass?: string;
  /** Direct hex/rgb color value for fallback */
  colorValue?: string;
}

export interface CustomColorItemProps extends HTMLAttributes<HTMLDivElement> {
  /** Title of the color group */
  title: string;
  /** Subtitle/description */
  subtitle?: string;
  /** Array of color swatches to display */
  colors: ColorSwatch[];
  /** Show Tailwind class examples */
  showTailwindClasses?: boolean;
  /** Show usage examples */
  showUsageExamples?: boolean;
  /** Custom usage examples */
  usageExamples?: string[];
}

/**
 * Custom ColorItem component that can handle both CSS custom properties and Tailwind classes
 */
export const CustomColorItem = ({
  title,
  subtitle,
  colors,
  showTailwindClasses = true,
  showUsageExamples = true,
  usageExamples,
  className = "",
  ...props
}: CustomColorItemProps) => {
  const baseClasses = "mb-8";

  return (
    <div className={`${baseClasses} ${className}`} {...props}>
      {/* Header */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-foreground">{title}</h3>
        {subtitle && (
          <p className="text-sm text-foreground-subdued">{subtitle}</p>
        )}
      </div>

      {/* Color Swatches */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
        {colors.map((color, index) => (
          <div key={index} className="flex flex-col items-center">
            {/* Color Swatch */}
            <div
              className={`w-16 h-16 rounded-lg border border-gray-300 shadow-sm mb-2 ${
                color.bgClass || ""
              }`}
              style={{
                backgroundColor: color.cssValue || color.colorValue,
              }}
              title={`${color.name}: ${color.cssValue || color.colorValue}`}
            />
            
            {/* Color Name */}
            <span className="text-xs font-medium text-center text-foreground">
              {color.name}
            </span>
            
            {/* CSS Value */}
            {color.cssValue && (
              <code className="text-xs text-foreground-subdued bg-gray-100 px-1 rounded mt-1">
                {color.cssValue}
              </code>
            )}
          </div>
        ))}
      </div>

      {/* Tailwind Classes */}
      {showTailwindClasses && (
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-foreground mb-2">
            Tailwind Classes:
          </h4>
          <div className="space-y-1">
            {colors.some((c) => c.bgClass) && (
              <div>
                <span className="text-xs font-medium text-foreground-subdued">
                  Background:{" "}
                </span>
                <code className="text-xs text-foreground bg-gray-100 px-1 rounded">
                  {colors
                    .filter((c) => c.bgClass)
                    .map((c) => c.bgClass)
                    .join(", ")}
                </code>
              </div>
            )}
            {colors.some((c) => c.textClass) && (
              <div>
                <span className="text-xs font-medium text-foreground-subdued">
                  Text:{" "}
                </span>
                <code className="text-xs text-foreground bg-gray-100 px-1 rounded">
                  {colors
                    .filter((c) => c.textClass)
                    .map((c) => c.textClass)
                    .join(", ")}
                </code>
              </div>
            )}
            {colors.some((c) => c.borderClass) && (
              <div>
                <span className="text-xs font-medium text-foreground-subdued">
                  Border:{" "}
                </span>
                <code className="text-xs text-foreground bg-gray-100 px-1 rounded">
                  {colors
                    .filter((c) => c.borderClass)
                    .map((c) => c.borderClass)
                    .join(", ")}
                </code>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Usage Examples */}
      {showUsageExamples && (usageExamples || colors.some((c) => c.bgClass)) && (
        <div>
          <h4 className="text-sm font-semibold text-foreground mb-2">
            Usage Examples:
          </h4>
          <div className="space-y-2">
            {usageExamples ? (
              usageExamples.map((example, index) => (
                <pre
                  key={index}
                  className="text-xs bg-gray-100 p-2 rounded overflow-x-auto"
                >
                  <code>{example}</code>
                </pre>
              ))
            ) : (
              // Auto-generate examples from Tailwind classes
              colors
                .filter((c) => c.bgClass)
                .slice(0, 2)
                .map((color, index) => (
                  <pre
                    key={index}
                    className="text-xs bg-gray-100 p-2 rounded overflow-x-auto"
                  >
                    <code>{`<div className="${color.bgClass} ${
                      color.textClass || "text-foreground"
                    } ${color.borderClass || "border border-gray-300"}">
  ${color.name} example
</div>`}</code>
                  </pre>
                ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomColorItem;
