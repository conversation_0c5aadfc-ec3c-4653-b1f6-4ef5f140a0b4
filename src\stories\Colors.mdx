import { Meta, ColorPalette, ColorItem } from '@storybook/blocks';

<Meta title="Design System/Colors" />

# Color Palette

This page showcases all the colors available in the RS Design System.

## Brand Colors

<ColorPalette>
  <ColorItem
    title="Brand Red"
    subtitle="Primary brand color"
    colors={{
      'brand-red': 'var(--color-brand-red)',
    }}
  />
  <ColorItem
    title="Brand Yellow"
    subtitle="Secondary brand color"
    colors={{
      'brand-yellow': 'var(--color-brand-yellow)',
    }}
  />
  <ColorItem
    title="Brand Sea"
    subtitle="Accent brand color"
    colors={{
      'brand-sea': 'var(--color-brand-sea)',
    }}
  />
  <ColorItem
    title="Brand Cream"
    subtitle="Light brand color"
    colors={{
      'brand-cream': 'var(--color-brand-cream)',
    }}
  />
</ColorPalette>

## Gray Scale

<ColorPalette>
  <ColorItem
    title="Gray Scale"
    subtitle="Neutral colors"
    colors={{
      'white': 'var(--color-white)',
      'gray-50': 'var(--color-gray-50)',
      'gray-100': 'var(--color-gray-100)',
      'gray-175': 'var(--color-gray-175)',
      'gray-200': 'var(--color-gray-200)',
      'gray-300': 'var(--color-gray-300)',
      'gray-400': 'var(--color-gray-400)',
      'gray-500': 'var(--color-gray-500)',
      'gray-600': 'var(--color-gray-600)',
      'gray-650': 'var(--color-gray-650)',
      'gray-700': 'var(--color-gray-700)',
      'gray-800': 'var(--color-gray-800)',
      'black': 'var(--color-black)',
    }}
  />
</ColorPalette>

## 1177 Color System

<ColorPalette>
  <ColorItem
    title="1177 Sky"
    subtitle="Sky color variants"
    colors={{
      'sky-dark': 'var(--color-1177-sky-dark)',
      'sky-base': 'var(--color-1177-sky-base)',
      'sky-clear': 'var(--color-1177-sky-clear)',
      'sky-line': 'var(--color-1177-sky-line)',
      'sky-background': 'var(--color-1177-sky-background)',
    }}
  />
  <ColorItem
    title="1177 Grass"
    subtitle="Grass color variants"
    colors={{
      'grass-dark': 'var(--color-1177-grass-dark)',
      'grass-base': 'var(--color-1177-grass-base)',
      'grass-clear': 'var(--color-1177-grass-clear)',
      'grass-line': 'var(--color-1177-grass-line)',
      'grass-background': 'var(--color-1177-grass-background)',
    }}
  />
  <ColorItem
    title="1177 Plum"
    subtitle="Plum color variants"
    colors={{
      'plum-dark': 'var(--color-1177-plum-dark)',
      'plum-base': 'var(--color-1177-plum-base)',
      'plum-clear': 'var(--color-1177-plum-clear)',
      'plum-line': 'var(--color-1177-plum-line)',
      'plum-background': 'var(--color-1177-plum-background)',
    }}
  />
  <ColorItem
    title="1177 Sun"
    subtitle="Sun color variants"
    colors={{
      'sun-dark': 'var(--color-1177-sun-dark)',
      'sun-base': 'var(--color-1177-sun-base)',
      'sun-clear': 'var(--color-1177-sun-clear)',
      'sun-background': 'var(--color-1177-sun-background)',
    }}
  />
  <ColorItem
    title="1177 Stone"
    subtitle="Stone color variants"
    colors={{
      'stone-dark': 'var(--color-1177-stone-dark)',
      'stone-base': 'var(--color-1177-stone-base)',
      'stone-clear': 'var(--color-1177-stone-clear)',
      'stone-line': 'var(--color-1177-stone-line)',
      'stone-background': 'var(--color-1177-stone-background)',
    }}
  />
</ColorPalette>

## Theme Colors

<ColorPalette>
  <ColorItem
    title="Foreground & Background"
    subtitle="Main theme colors"
    colors={{
      'foreground': 'var(--color-foreground)',
      'foreground-subdued': 'var(--color-foreground-subdued)',
      'background': 'var(--color-background)',
    }}
  />
  <ColorItem
    title="Interactive Colors"
    subtitle="Links, borders, focus states"
    colors={{
      'border': 'var(--color-border)',
      'border-selected': 'var(--color-border-selected)',
      'link': 'var(--color-link)',
      'link-visited': 'var(--color-link--visited)',
      'focus': 'var(--color-focus)',
      'warning': 'var(--color-warning)',
    }}
  />
</ColorPalette>

## Button Colors

<ColorPalette>
  <ColorItem
    title="Main Button"
    subtitle="Primary button colors"
    colors={{
      'main-button-fg': 'var(--color-ct-main-button-fg)',
      'main-button-bg': 'var(--color-ct-main-button-bg)',
      'main-button-bg-hover': 'var(--color-ct-main-button-bg--hover)',
      'main-button-bg-active': 'var(--color-ct-main-button-bg--active)',
    }}
  />
  <ColorItem
    title="Default Button"
    subtitle="Secondary button colors"
    colors={{
      'button-bg': 'var(--color-ct-button-bg)',
      'button-bg-hover': 'var(--color-ct-button-bg--hover)',
      'button-bg-active': 'var(--color-ct-button-bg--active)',
      'button-bg-selected': 'var(--color-ct-button-bg--selected)',
    }}
  />
  <ColorItem
    title="Ghost Button"
    subtitle="Tertiary button colors"
    colors={{
      'ghost-button-bg': 'var(--color-ct-ghost-button-bg)',
      'ghost-button-bg-hover': 'var(--color-ct-ghost-button-bg--hover)',
      'ghost-button-bg-active': 'var(--color-ct-ghost-button-bg--active)',
      'ghost-button-bg-selected': 'var(--color-ct-ghost-button-bg--selected)',
    }}
  />
</ColorPalette>

## Logo Colors

<ColorPalette>
  <ColorItem
    title="Logo Colors"
    subtitle="Colors used in logos"
    colors={{
      'logo-primary': 'var(--color-logo-primary)',
      'logo-secondary': 'var(--color-logo-secondar)',
      'logo-black-gray-primary': 'var(--color-logo-black-gray-primary)',
      'logo-black-gray-secondary': 'var(--color-logo-black-gray-secondary)',
    }}
  />
</ColorPalette>